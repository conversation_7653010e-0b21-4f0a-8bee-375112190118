'use client';

import Image from 'next/image';
import Link from 'next/link';
import TypewriterEffect from './TypewriterEffect';

export default function Hero() {
  return (
    <section className="relative h-screen w-full overflow-hidden font-sans">
      {/* Background Video */}
      <video
        src="/video.mp4"
        autoPlay
        muted
        loop
        playsInline
        className="absolute inset-0 h-full w-full object-cover -z-20"
      />

      {/* Overlay */}
      <div className="absolute inset-0 bg-black/80 -z-10" />

      {/* Main Container with Grid Layout */}
      <div className="relative z-10 h-full w-full">
        {/* Grid Container */}
        <div className="h-full grid grid-cols-12 gap-4 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-20 max-w-7xl mx-auto">

          {/* Header/Logo Section - Spans full width */}
          <div className="col-span-12 pt-6 sm:pt-8">
            <div className="flex items-center gap-3">
              <img
                src="/logo-icon.png"
                alt="Logo Icon"
                className="w-8 h-8 sm:w-10 sm:h-10 flex-shrink-0"
              />
              <div>
                <h1 className="text-white font-semibold text-lg sm:text-xl md:text-2xl leading-tight">
                  Prolytech
                </h1>
                <p className="text-[#05A0E2] text-[5px] sm:text-[5.5px] md:text-[6px] font-bold tracking-wide uppercase">
                  DEFINING THE CURVE OF WHAT'S NEXT
                </p>
              </div>
            </div>
          </div>

          {/* Empty columns 1-2 for spacing */}
          <div className="hidden lg:block lg:col-span-2"></div>

          {/* Main Content Section - Starts from 3rd grid column */}
          <div className="col-span-12 lg:col-span-8 xl:col-span-7 self-end pb-12 sm:pb-16 md:pb-20 lg:pb-24">
            {/* Main Heading */}
            <h1 className="text-white text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-semibold leading-tight mb-4 sm:mb-6">
              <span className="block mb-2">Powering the Next Wave</span>
              <span className="flex flex-wrap items-center gap-2">
                <span>of</span>
                <TypewriterEffect
                  phrases={[
                    'Digital Innovation',
                    'Intelligent Automation',
                    'Scalable Growth',
                    'Cloud Scalability'
                  ]}
                  className="text-cyan-400"
                  typingSpeed={50}
                  deletingSpeed={25}
                  pauseDuration={2500}
                />
              </span>
            </h1>

            {/* Description */}
            <p className="text-gray-200 text-sm sm:text-base md:text-lg lg:text-xl leading-relaxed mb-6 sm:mb-8 max-w-2xl">
              We architect and deliver high-performance platforms—social, transactional,
              and intelligent—at startup speed and enterprise scale.
            </p>

            {/* CTA Button */}
            <Link
              href="/contact"
              className="inline-block px-6 sm:px-8 py-3 sm:py-4 text-white font-medium text-sm sm:text-base md:text-lg
                rounded-full shadow-lg transition-all duration-300 transform hover:scale-105
                bg-[linear-gradient(to_bottom,#04E6FC,#0664CC)] hover:bg-[linear-gradient(to_bottom,#04c9de,#0559b4)]
                hover:shadow-xl"
            >
              Schedule a Consultation
            </Link>
          </div>

          {/* Right spacing column */}
          <div className="hidden xl:block xl:col-span-2"></div>
        </div>
      </div>
    </section>
  );
}
