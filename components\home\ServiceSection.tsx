'use client';

import Image from "next/image";
import { services } from "./services";

export default function ServicesSection() {
  return (
    <section className="bg-white py-16 overflow-hidden">

      {/* Main Content */}
      <div className="col-span-12 lg:col-span-12 xl:col-span-7 self-start">
       {/* Heading */}
<div className="mb-12 lg:ml-[33.33%] lg:mr-[10%] ">
  <h3 className="text-[#05A0E2] text-[14px] font-medium tracking-[0.61px] uppercase leading-[17.5px]" style={{ fontFamily: 'Inter' }}>
    Who We Are
  </h3>
  <h2 className="text-[32px] font-normal text-black mt-2 mb-4 leading-[40px] tracking-[0%]" style={{ fontFamily: 'Inter' }}>
    Agile. Cloud-first. Impact-driven
  </h2>
  <div className="text-[#8F8F8F] text-[14px] font-[400] leading-[20px] tracking-[0%]" style={{ fontFamily: 'Inter' }}>
    <span className="">Prolytech Solutions is a modern technology company focused on crafting scalable digital ecosystems.</span><br/>
    <span className="">Our team specializes in building cloud-native applications, real-time engagement platforms, intelligent</span><br/>
    <span className="">automation systems, and secure infrastructures—designed to serve millions and built for longevity.</span>
  </div>
</div>


        {/* Services Grid */}
        <div className="grid grid-cols-12 sm:grid-cols-2 md:grid-cols-3 gap-0">
          {/* First Card Static */}
          <div className="bg-[#F4F4F4] flex flex-col items-center justify-center h-64 md:h-[300px] text-center">
            <h3
              className="text-[#05A0E2] text-[14px] font-medium tracking-[0.61px] uppercase leading-[17.5px]"
              style={{ fontFamily: "Inter" }}
            >
              OUR SERVICES
            </h3>
            <p
              className="text-black text-[32px] mt-1 font-normal leading-[30px] tracking-[0%] ml-18"
              style={{ fontFamily: "Inter" }}
            >
              What We Do
            </p>
          </div>

          {/* Other Service Cards */}
          {services.map((service, index) => {
            const Icon = service.icon;
            const isComponent = typeof Icon !== "string";

            return (
              <div
                key={index}
                className="relative group h-64 md:h-[300px] w-full overflow-hidden"
              >
                {/* Background Image */}
                <Image
                  src={service.img}
                  alt={service.title}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                  width={500}
                  height={300}
                />

                {/* Overlay */}
                <div className="absolute inset-0 bg-black/40 group-hover:bg-black/50 transition duration-300" />

                {/* Icon + Title */}
                <div className="absolute bottom-4 left-4 z-10 flex flex-col items-start ml-7 pb-2">
                  <div className="bg-[#1e1e1e] p-2 rounded-md mb-2">
                    {isComponent ? (
                      <Icon className="w-8 h-8 text-white" />
                    ) : (
                      <Image
                        src={Icon}
                        alt="Icon"
                        width={32}
                        height={32}
                        className="w-8 h-8"
                      />
                    )}
                  </div>
                  <h3 className="text-white text-lg font-medium">
                    {service.title}
                  </h3>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}